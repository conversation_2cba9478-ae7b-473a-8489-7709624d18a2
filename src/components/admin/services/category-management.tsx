'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  FolderOpenIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  EyeIcon,
  EyeSlashIcon,
  MagnifyingGlassIcon,
  ListBulletIcon,
  Squares2X2Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { CategoryHeader } from './category-header'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface CategoryManagementProps {
  selectedCategory: Category | null
  onCategorySelect: (category: Category | null) => void
}

interface CategoryFormData {
  name: string
  description: string
  parentId: string
  isActive: boolean
  displayOrder: number
}

// Function to get category-specific icons based on category name/type
const getCategoryIcon = (category: Category): string => {
  const name = category.name.toLowerCase()

  // Web Development related
  if (name.includes('web') || name.includes('website') || name.includes('frontend') || name.includes('backend')) {
    return 'fa-globe text-blue-500'
  }
  // Mobile Development
  if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) {
    return 'fa-mobile-alt text-green-500'
  }
  // Design related
  if (name.includes('design') || name.includes('ui') || name.includes('ux') || name.includes('graphic')) {
    return 'fa-palette text-purple-500'
  }
  // E-commerce
  if (name.includes('ecommerce') || name.includes('shop') || name.includes('store') || name.includes('commerce')) {
    return 'fa-shopping-cart text-orange-500'
  }
  // Marketing
  if (name.includes('marketing') || name.includes('seo') || name.includes('social') || name.includes('advertising')) {
    return 'fa-bullhorn text-red-500'
  }
  // Consulting
  if (name.includes('consulting') || name.includes('strategy') || name.includes('business')) {
    return 'fa-handshake text-indigo-500'
  }
  // Support/Maintenance
  if (name.includes('support') || name.includes('maintenance') || name.includes('hosting')) {
    return 'fa-tools text-gray-500'
  }
  // Security
  if (name.includes('security') || name.includes('ssl') || name.includes('backup')) {
    return 'fa-shield-alt text-yellow-500'
  }
  // Analytics
  if (name.includes('analytics') || name.includes('tracking') || name.includes('report')) {
    return 'fa-chart-line text-teal-500'
  }
  // Default icons
  if (category.parentId) {
    return 'fa-tag text-orange-500' // Subcategory
  }
  return 'fa-layer-group text-blue-500' // Parent category
}

// Custom drag and resize hook
const useDraggableResizable = (isFormOpen: boolean) => {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [size, setSize] = useState({ width: 500, height: 600 })
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [resizeStart, setResizeStart] = useState({ width: 0, height: 0, x: 0, y: 0 })
  const elementRef = useRef<HTMLDivElement>(null)

  // Optimized mouse move handler with throttling
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.x
      const newY = e.clientY - dragStart.y
      
      // Constraints - Allow full screen dragging
      const maxX = window.innerWidth - 100
      const maxY = window.innerHeight - 50
      const minX = -(window.innerWidth - 100)
      const minY = -(window.innerHeight - 50)
      
      const constrainedPosition = {
        x: Math.max(minX, Math.min(maxX, newX)),
        y: Math.max(minY, Math.min(maxY, newY))
      }
      
      setPosition(constrainedPosition)
      
      // Direct DOM manipulation
      if (elementRef.current) {
        elementRef.current.style.setProperty('left', `calc(50% + ${constrainedPosition.x}px)`, 'important')
        elementRef.current.style.setProperty('top', `calc(50% + ${constrainedPosition.y}px)`, 'important')
        elementRef.current.style.setProperty('transform', 'none', 'important')
      }
    } else if (isResizing) {
      const deltaX = e.clientX - resizeStart.x
      const deltaY = e.clientY - resizeStart.y
      
      const newWidth = Math.max(400, Math.min(800, resizeStart.width + deltaX))
      const newHeight = Math.max(300, Math.min(700, resizeStart.height + deltaY))
      
      setSize({ width: newWidth, height: newHeight })
      
      // Update modal size
      if (elementRef.current) {
        elementRef.current.style.setProperty('width', `${newWidth}px`, 'important')
        elementRef.current.style.setProperty('max-height', `${newHeight}px`, 'important')
      }
    }
  }, [isDragging, isResizing, dragStart, resizeStart])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
    setIsResizing(false)
  }, [])

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    })
  }, [position])

  const handleResizeMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
    setResizeStart({
      width: size.width,
      height: size.height,
      x: e.clientX,
      y: e.clientY
    })
  }, [size])

  // Event listeners
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  // Reset when modal opens
  useEffect(() => {
    if (isFormOpen) {
      setPosition({ x: 0, y: 0 })
      setSize({ width: 500, height: 600 })
      if (elementRef.current) {
        elementRef.current.style.setProperty('left', '50%', 'important')
        elementRef.current.style.setProperty('top', '50%', 'important')
        elementRef.current.style.setProperty('transform', 'translate(-50%, -50%)', 'important')
        elementRef.current.style.setProperty('width', '500px', 'important')
        elementRef.current.style.setProperty('max-height', '600px', 'important')
      }
    }
  }, [isFormOpen])

  return {
    position,
    size,
    isDragging,
    isResizing,
    handleMouseDown,
    handleResizeMouseDown,
    elementRef
  }
}

export function CategoryManagement({ selectedCategory, onCategorySelect }: CategoryManagementProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [currentFilters, setCurrentFilters] = useState<Record<string, string>>({})
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>('comfortable')
  const { position, size, isDragging, isResizing, handleMouseDown, handleResizeMouseDown, elementRef } = useDraggableResizable(isFormOpen)

  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    parentId: '',
    isActive: true,
    displayOrder: 0
  })

  const filters = [
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ]
    },
    {
      key: 'parent',
      label: 'Parent Category',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Categories' },
        { value: 'root', label: 'Root Categories' },
        { value: 'sub', label: 'Sub Categories' }
      ]
    }
  ]

  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    filterAndSortCategories()
  }, [categories, searchQuery, currentFilters])



  const filterAndSortCategories = () => {
    let filtered = [...categories]

    // Apply search filter
    if (searchQuery.trim()) {
      const searchLower = searchQuery.toLowerCase()
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter
    if (currentFilters.status) {
      if (currentFilters.status === 'active') {
        filtered = filtered.filter(category => category.isActive)
      } else if (currentFilters.status === 'inactive') {
        filtered = filtered.filter(category => !category.isActive)
      }
    }

    // Apply parent filter
    if (currentFilters.parent) {
      if (currentFilters.parent === 'root') {
        filtered = filtered.filter(category => !category.parentId)
      } else if (currentFilters.parent === 'sub') {
        filtered = filtered.filter(category => category.parentId)
      }
    }

    setFilteredCategories(filtered)
  }

  const buildCategoryTree = (flatCategories: any[]): Category[] => {
    const categoryMap = new Map()
    const rootCategories: Category[] = []

    // Transform and create map
    flatCategories.forEach(cat => {
      const category: Category = {
        id: String(cat.id),
        name: cat.categname || cat.name,
        description: cat.categdesc || cat.description,
        parentId: cat.parentid ? String(cat.parentid) : undefined,
        isActive: cat.isactive,
        displayOrder: cat.displayorder || 0,
        children: [],
        _count: cat._count
      }
      categoryMap.set(category.id, category)
    })

    // Build tree structure
    categoryMap.forEach(category => {
      if (category.parentId && categoryMap.has(category.parentId)) {
        categoryMap.get(category.parentId).children.push(category)
      } else {
        rootCategories.push(category)
      }
    })

    // Sort by display order
    const sortCategories = (cats: Category[]) => {
      cats.sort((a, b) => a.displayOrder - b.displayOrder)
      cats.forEach(cat => {
        if (cat.children) {
          sortCategories(cat.children)
        }
      })
    }

    sortCategories(rootCategories)
    return rootCategories
  }

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/categories?limit=100')

      if (response.ok) {
        const data = await response.json()
        const categoriesData = data.data || data.categories || []
        setCategories(buildCategoryTree(categoriesData))
      } else {
        console.error('Failed to fetch categories:', response.status, response.statusText)
        setCategories([])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      parentId: category.parentId || '',
      isActive: category.isActive,
      displayOrder: category.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchCategories()
        if (selectedCategory?.id === category.id) {
          onCategorySelect(null)
        }
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to delete category')
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('An error occurred while deleting the category')
    }
  }

  const handleToggleActive = async (category: Category) => {
    try {
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          categname: category.name,
          categdesc: category.description,
          parentid: category.parentId ? Number(category.parentId) : 0,
          isactive: !category.isActive,
          displayorder: category.displayOrder
        })
      })
      if (response.ok) {
        fetchCategories()
      }
    } catch (error) {
      console.error('Error toggling category status:', error)
    }
  }

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const renderCategory = (category: Category, level: number = 0) => {
    const isExpanded = expandedCategories.has(category.id)
    const hasChildren = category.children && category.children.length > 0
    const isSelected = selectedCategory?.id === category.id

    return (
      <div key={category.id} className="select-none">
        <div
          className={`flex items-center justify-between px-2 rounded-none cursor-pointer transition-colors border border-gray-200 ${
            isSelected
              ? 'bg-blue-50 border-blue-300'
              : 'bg-white hover:bg-gray-50'
          }`}
          onClick={() => onCategorySelect(category)}
        >
          {/* Expand/Collapse Button */}
          <div className="w-4" style={{ marginLeft: `${level * 20}px` }}>
            {hasChildren ? (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  toggleExpanded(category.id)
                }}
                className="p-0.5 hover:bg-gray-200 rounded-none"
              >
                {isExpanded ? (
                  <ChevronDownIcon className="h-3 w-3 text-gray-500" />
                ) : (
                  <ChevronRightIcon className="h-3 w-3 text-gray-500" />
                )}
              </button>
            ) : (
              <div className="w-4" />
            )}
          </div>

          {/* Category Icon */}
          <div className="w-8 h-full flex items-center justify-start">
            <i className={`fas ${getCategoryIcon(category)} text-3xl`}></i>
          </div>

          {/* Category Name & Description */}
          <div className="flex-1 min-w-0 flex flex-col justify-center ml-3">
            <h3 className={`font-bold text-base truncate ${isSelected ? 'text-gray-900' : 'text-gray-900'}`}>
              {category.name}
            </h3>
            {category.description && (
              <p className={`text-sm truncate mt-0.5 ${isSelected ? 'text-gray-600' : 'text-gray-600'}`}>
                {category.description}
              </p>
            )}
          </div>

          {/* Parent Category */}
          <div className="w-32 flex items-center">
            {category.parentId ? (
              <span className="text-sm text-gray-600 truncate block">
                {categories.find(c => c.id === category.parentId)?.name || 'Unknown'}
              </span>
            ) : (
              <span className="text-sm text-gray-400 italic">Root</span>
            )}
          </div>

          {/* Services Count */}
          <div className="w-24 flex items-center">
            {category._count && typeof category._count.services === 'number' && (
              <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-none text-sm font-medium">
                {category._count.services}
              </span>
            )}
          </div>

          {/* Status */}
          <div className="w-20 flex items-center">
            <span className={`inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium ${
              category.isActive
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {category.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2 w-32">

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEdit(category)
                    }}
                    className="inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    title="Edit category"
                  >
                    <PencilIcon className="h-3 w-3" />
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleToggleActive(category)
                    }}
                    className={`inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white ${
                      category.isActive
                        ? 'bg-blue-400 hover:bg-blue-500 focus:ring-blue-500'
                        : 'bg-blue-300 hover:bg-blue-400 focus:ring-blue-500'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                    title={category.isActive ? 'Deactivate category' : 'Activate category'}
                  >
                    {category.isActive ? (
                      <EyeSlashIcon className="h-3 w-3" />
                    ) : (
                      <EyeIcon className="h-3 w-3" />
                    )}
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete(category)
                    }}
                    className="inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    title="Delete category"
                  >
                    <TrashIcon className="h-3 w-3" />
                  </button>
            </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="ml-6">
            {category.children?.map((child) => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  const handleCreateClick = () => {
    setIsFormOpen(true)
    setEditingCategory(null)
    setFormData({
      name: '',
      description: '',
      parentId: '',
      isActive: true,
      displayOrder: 0
    })
  }

  const handleFiltersChange = (newFilters: Record<string, string>) => {
    setCurrentFilters(newFilters)
  }

  const renderCategoryCard = (category: Category, isLargeCard: boolean = false) => {
    const isSelected = selectedCategory?.id === category.id
    const hasChildren = category.children && category.children.length > 0

    return (
      <div
        key={category.id}
        className={`group relative bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-300 overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md ${
          isSelected
            ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300'
            : 'hover:bg-gray-50/50 hover:border-gray-300'
        } ${isLargeCard ? 'p-5' : 'p-4'}`}
        onClick={() => onCategorySelect(category)}
      >
        {/* Header Section */}
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            {/* Icon Container */}
            <div className="flex-shrink-0 p-2 bg-blue-50 rounded-lg">
              <i className={`fas ${getCategoryIcon(category)} text-2xl`}></i>
            </div>
            
            {/* Title and Description */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-gray-900 truncate mb-2">
                {category.name}
              </h3>
              {category.description && (
                <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                  {category.description}
                </p>
              )}
            </div>
          </div>

                  {/* Status Badge */}
        <span className={`inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium ${
          category.isActive
            ? 'bg-green-100 text-green-800 border border-green-200'
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
            category.isActive ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          {category.isActive ? 'Active' : 'Inactive'}
        </span>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-100 my-1"></div>

        {/* Info Grid */}
        <div className="grid grid-cols-2 gap-4 mb-1">
          {/* Parent Category */}
          <div className="space-y-0.5">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Parent</div>
            <div className="text-sm font-medium text-gray-600">
              {category.parentId ? 'Sub-category' : 'Main Category'}
            </div>
          </div>

          {/* Services Count */}
          <div className="space-y-0.5">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Services</div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-1 py-0 bg-blue-100 text-blue-800 text-sm font-medium rounded-md">
                {category._count?.services || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-1 border-t border-gray-100">
          <span className="flex items-center space-x-1">
            <span className="font-medium">Children:</span>
            <span className="bg-gray-100 px-1 py-0 rounded">{category._count?.children || 0}</span>
          </span>
          <span className="flex items-center space-x-1">
            <span className="font-medium">Order:</span>
            <span>{category.displayOrder}</span>
          </span>
        </div>

        {/* Actions Sidebar - Professional Overlay */}
        <div className="absolute top-3 right-3 bottom-3 w-12 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out transform translate-x-full group-hover:translate-x-0 flex flex-col items-center justify-center space-y-4 z-10">
          {/* Edit Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleEdit(category)
            }}
            className="group/btn relative inline-flex items-center justify-center w-8 h-8 bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110"
            title="Edit Category"
          >
            <PencilIcon className="h-4 w-4 transition-transform group-hover/btn:scale-110" />
          </button>
          
          {/* Toggle Active Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleToggleActive(category)
            }}
            className={`group/btn relative inline-flex items-center justify-center w-8 h-8 border rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110 ${
              category.isActive
                ? 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600 text-white focus:ring-green-500'
                : 'bg-orange-500 hover:bg-orange-600 border-orange-400 hover:border-orange-500 text-white focus:ring-orange-500'
            }`}
            title={category.isActive ? 'Deactivate Category' : 'Activate Category'}
          >
            {category.isActive ? (
              <EyeSlashIcon className="h-4 w-4 transition-transform group-hover/btn:scale-110" />
            ) : (
              <EyeIcon className="h-4 w-4 transition-transform group-hover/btn:scale-110" />
            )}
          </button>
          
          {/* Delete Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleDelete(category)
            }}
            className="group/btn relative inline-flex items-center justify-center w-8 h-8 bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110"
            title="Delete Category"
          >
            <TrashIcon className="h-4 w-4 transition-transform group-hover/btn:scale-110" />
          </button>
        </div>
      </div>
    )
  }

  const getAllCategories = (cats: Category[]): Category[] => {
    let all: Category[] = []
    cats.forEach(cat => {
      all.push(cat)
      if (cat.children && cat.children.length > 0) {
        all = all.concat(getAllCategories(cat.children))
      }
    })
    return all
  }

  return (
    <div className="space-y-3">
      {/* Search and View Controls */}
      <div className="flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Search Bar */}
        <div className="relative flex-1 max-w-md">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search categories by name or description..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        </div>

        {/* View Options and Create Button */}
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">View:</span>
            <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded-md transition-colors flex items-center gap-2 ${
                  viewMode === 'list'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List view"
              >
                <ListBulletIcon className="h-5 w-5" />
                <span className="text-sm font-medium">List</span>
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 rounded-md transition-colors flex items-center gap-2 ${
                  viewMode === 'grid'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-5 w-5" />
                <span className="text-sm font-medium">Grid</span>
              </button>
            </div>
          </div>

          {/* Create Button */}
          <button
            onClick={handleCreateClick}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Category
          </button>
        </div>
      </div>

      {/* Categories List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading categories...</p>
          </div>
        ) : filteredCategories.length === 0 ? (
          <div className="p-6 text-center">
            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || Object.keys(currentFilters).some(key => currentFilters[key]) 
                ? 'Try adjusting your search terms or filters.' 
                : 'Get started by creating your first category.'}
            </p>
            <button
              onClick={handleCreateClick}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Category
            </button>
          </div>
        ) : (
          <div>
            {viewMode === 'list' && (
              <div>
                {/* List Headers */}
                <div className="bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2">
                  <div className="flex items-center">
                    <div className="w-6"></div> {/* Space for expand/collapse button */}
                    <div className="w-6"></div> {/* Space for folder icon */}
                    <div className="flex-1 min-w-0">
                      <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Category</span>
                    </div>
                    <div className="w-32">
                      <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Parent</span>
                    </div>
                    <div className="w-24">
                      <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Services</span>
                    </div>
                    <div className="w-20">
                      <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Status</span>
                    </div>
                    <div className="w-32">
                      <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</span>
                    </div>
                  </div>
                </div>

                {/* List Items */}
                <div className="space-y-1">
                  {filteredCategories.map((category) => renderCategory(category))}
                </div>
              </div>
            )}

            {viewMode === 'grid' && (
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {getAllCategories(filteredCategories).map((category) => renderCategoryCard(category, false))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            ref={elementRef}
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            style={{
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 9999,
              cursor: isDragging ? 'grabbing' : 'default',
              width: `${size.width}px`,
              maxHeight: `${size.height}px`
            }}
            className="bg-white rounded-xl shadow-2xl border border-gray-200 p-0 overflow-hidden draggable-modal"
            onClick={(e) => e.stopPropagation()}
          >
              {/* Clean Professional Modal Header - Drag Handle */}
              <div 
                className="bg-gradient-to-r from-blue-500 to-blue-600 px-5 py-3 flex items-center justify-between cursor-move select-none border-b-2 border-blue-700 shadow-sm"
                onMouseDown={handleMouseDown}
                style={{ cursor: 'move', userSelect: 'none' }}
              >
                <div className="flex items-center space-x-3">
                  <PencilIcon className="h-5 w-5 text-white" />
                  <h3 className="text-white font-semibold text-lg tracking-wide bg-blue-500">
                    {editingCategory ? 'Edit Category' : 'New Category'}
                  </h3>
                </div>
                <button
                  type="button"
                  onClick={() => setIsFormOpen(false)}
                  className="text-white hover:text-blue-100 transition-colors"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>

              {/* Compact Form Content */}
              <div className="p-4 overflow-y-auto cursor-default form-content scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100" style={{ maxHeight: `${size.height - 80}px` }}>
              
              <form onSubmit={async (e) => {
                e.preventDefault()
                
                try {
                  const url = editingCategory 
                    ? `/api/admin/categories/${editingCategory.id}`
                    : '/api/admin/categories'
                  
                  const method = editingCategory ? 'PUT' : 'POST'
                  
                  const response = await fetch(url, {
                    method,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      categname: formData.name,
                      categdesc: formData.description,
                      parentid: formData.parentId ? Number(formData.parentId) : 0,
                      isactive: formData.isActive,
                      displayorder: formData.displayOrder
                    })
                  })

                  if (response.ok) {
                    setIsFormOpen(false)
                    fetchCategories()
                  } else {
                    const errorData = await response.json()
                    alert(errorData.message || 'Failed to save category')
                  }
                } catch (error) {
                  console.error('Error saving category:', error)
                  alert('An error occurred while saving the category')
                }
              }}>
                <div className="space-y-3">
                  {/* Name Field */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Name *
                    </label>
                    <input
                      type="text"
                      required
                      maxLength={50}
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Category name"
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="text-xs text-gray-400 mt-1">{formData.name.length}/50</p>
                  </div>

                  {/* Description Field */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Brief description"
                      rows={2}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    />
                  </div>

                  {/* Parent Category */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Parent Category
                    </label>
                    <select
                      value={formData.parentId}
                      onChange={(e) => setFormData({ ...formData, parentId: e.target.value })}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">No parent (root category)</option>
                      {categories
                        .filter(cat => !editingCategory || cat.id !== editingCategory.id)
                        .map((cat) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Settings Row */}
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Display Order
                      </label>
                      <div className="relative w-24">
                        <input
                          type="number"
                          min="0"
                          value={formData.displayOrder}
                          onChange={(e) => setFormData({ ...formData, displayOrder: Number(e.target.value) })}
                          className="w-full px-3 py-2 pr-8 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-300 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        />
                        <div className="absolute right-2 top-1 bottom-1 flex flex-col justify-center">
                          <button
                            type="button"
                            onClick={() => setFormData({ ...formData, displayOrder: formData.displayOrder + 1 })}
                            className="w-3 h-2.5 flex items-center justify-center text-gray-400 text-xs leading-none bg-transparent border-0 outline-none mb-1"
                          >
                            ▲
                          </button>
                          <button
                            type="button"
                            onClick={() => setFormData({ ...formData, displayOrder: Math.max(0, formData.displayOrder - 1) })}
                            className="w-3 h-2.5 flex items-center justify-center text-gray-400 text-xs leading-none bg-transparent border-0 outline-none"
                          >
                            ▼
                          </button>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <div className="flex items-center h-[36px]">
                        <input
                          type="checkbox"
                          checked={formData.isActive}
                          onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                          className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-xs text-gray-700">
                          {formData.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                  {/* Form Actions */}
                  <div className="flex justify-end space-x-2 pt-3 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => setIsFormOpen(false)}
                      className="px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-3 py-1.5 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                    >
                      {editingCategory ? 'Update' : 'Create'}
                    </button>
                  </div>
                </form>
              </div>
              
              {/* Resize Handle */}
              <div
                className="absolute bottom-0 right-0 w-8 h-8 cursor-se-resize bg-blue-500 hover:bg-blue-600 rounded-tl-lg transition-colors shadow-lg border-2 border-white"
                onMouseDown={handleResizeMouseDown}
                style={{ cursor: 'se-resize' }}
                title="Drag to resize"
              >
                <div className="w-full h-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22 22H20V20H22V22ZM22 18H20V16H22V18ZM18 22H16V20H18V22ZM18 18H16V16H18V18ZM14 22H12V20H14V22ZM22 14H20V12H22V14Z"/>
                  </svg>
                </div>
              </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
