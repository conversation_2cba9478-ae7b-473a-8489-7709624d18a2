"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/category-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/category-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryManagement: () => (/* binding */ CategoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ CategoryManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Function to get category-specific icons based on category name/type\nconst getCategoryIcon = (category)=>{\n    const name = category.name.toLowerCase();\n    // Web Development related\n    if (name.includes('web') || name.includes('website') || name.includes('frontend') || name.includes('backend')) {\n        return 'fa-globe text-blue-500';\n    }\n    // Mobile Development\n    if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) {\n        return 'fa-mobile-alt text-green-500';\n    }\n    // Design related\n    if (name.includes('design') || name.includes('ui') || name.includes('ux') || name.includes('graphic')) {\n        return 'fa-palette text-purple-500';\n    }\n    // E-commerce\n    if (name.includes('ecommerce') || name.includes('shop') || name.includes('store') || name.includes('commerce')) {\n        return 'fa-shopping-cart text-orange-500';\n    }\n    // Marketing\n    if (name.includes('marketing') || name.includes('seo') || name.includes('social') || name.includes('advertising')) {\n        return 'fa-bullhorn text-red-500';\n    }\n    // Consulting\n    if (name.includes('consulting') || name.includes('strategy') || name.includes('business')) {\n        return 'fa-handshake text-indigo-500';\n    }\n    // Support/Maintenance\n    if (name.includes('support') || name.includes('maintenance') || name.includes('hosting')) {\n        return 'fa-tools text-gray-500';\n    }\n    // Security\n    if (name.includes('security') || name.includes('ssl') || name.includes('backup')) {\n        return 'fa-shield-alt text-yellow-500';\n    }\n    // Analytics\n    if (name.includes('analytics') || name.includes('tracking') || name.includes('report')) {\n        return 'fa-chart-line text-teal-500';\n    }\n    // Default icons\n    if (category.parentId) {\n        return 'fa-tag text-orange-500' // Subcategory\n        ;\n    }\n    return 'fa-layer-group text-blue-500' // Parent category\n    ;\n};\n// Custom drag and resize hook\nconst useDraggableResizable = (isFormOpen)=>{\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [size, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 500,\n        height: 600\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [resizeStart, setResizeStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n    });\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Optimized mouse move handler with throttling\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseMove]\": (e)=>{\n            if (isDragging) {\n                const newX = e.clientX - dragStart.x;\n                const newY = e.clientY - dragStart.y;\n                // Constraints - Allow full screen dragging\n                const maxX = window.innerWidth - 100;\n                const maxY = window.innerHeight - 50;\n                const minX = -(window.innerWidth - 100);\n                const minY = -(window.innerHeight - 50);\n                const constrainedPosition = {\n                    x: Math.max(minX, Math.min(maxX, newX)),\n                    y: Math.max(minY, Math.min(maxY, newY))\n                };\n                setPosition(constrainedPosition);\n                // Direct DOM manipulation\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('left', \"calc(50% + \".concat(constrainedPosition.x, \"px)\"), 'important');\n                    elementRef.current.style.setProperty('top', \"calc(50% + \".concat(constrainedPosition.y, \"px)\"), 'important');\n                    elementRef.current.style.setProperty('transform', 'none', 'important');\n                }\n            } else if (isResizing) {\n                const deltaX = e.clientX - resizeStart.x;\n                const deltaY = e.clientY - resizeStart.y;\n                const newWidth = Math.max(400, Math.min(800, resizeStart.width + deltaX));\n                const newHeight = Math.max(300, Math.min(700, resizeStart.height + deltaY));\n                setSize({\n                    width: newWidth,\n                    height: newHeight\n                });\n                // Update modal size\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('width', \"\".concat(newWidth, \"px\"), 'important');\n                    elementRef.current.style.setProperty('max-height', \"\".concat(newHeight, \"px\"), 'important');\n                }\n            }\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseMove]\"], [\n        isDragging,\n        isResizing,\n        dragStart,\n        resizeStart\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            setIsResizing(false);\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseUp]\"], []);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragging(true);\n            setDragStart({\n                x: e.clientX - position.x,\n                y: e.clientY - position.y\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseDown]\"], [\n        position\n    ]);\n    const handleResizeMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleResizeMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsResizing(true);\n            setResizeStart({\n                width: size.width,\n                height: size.height,\n                x: e.clientX,\n                y: e.clientY\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleResizeMouseDown]\"], [\n        size\n    ]);\n    // Event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isDragging || isResizing) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n            }\n            return ({\n                \"useDraggableResizable.useEffect\": ()=>{\n                    document.removeEventListener('mousemove', handleMouseMove);\n                    document.removeEventListener('mouseup', handleMouseUp);\n                }\n            })[\"useDraggableResizable.useEffect\"];\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    // Reset when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isFormOpen) {\n                setPosition({\n                    x: 0,\n                    y: 0\n                });\n                setSize({\n                    width: 500,\n                    height: 600\n                });\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('left', '50%', 'important');\n                    elementRef.current.style.setProperty('top', '50%', 'important');\n                    elementRef.current.style.setProperty('transform', 'translate(-50%, -50%)', 'important');\n                    elementRef.current.style.setProperty('width', '500px', 'important');\n                    elementRef.current.style.setProperty('max-height', '600px', 'important');\n                }\n            }\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isFormOpen\n    ]);\n    return {\n        position,\n        size,\n        isDragging,\n        isResizing,\n        handleMouseDown,\n        handleResizeMouseDown,\n        elementRef\n    };\n};\n_s(useDraggableResizable, \"HKfFlj2o7uMYOp6GelXY4F24h+I=\");\nfunction CategoryManagement(param) {\n    let { selectedCategory, onCategorySelect } = param;\n    var _this = this;\n    _s1();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentFilters, setCurrentFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('comfortable');\n    const { position, size, isDragging, isResizing, handleMouseDown, handleResizeMouseDown, elementRef } = useDraggableResizable(isFormOpen);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        parentId: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    const filters = [\n        {\n            key: 'status',\n            label: 'Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Status'\n                },\n                {\n                    value: 'active',\n                    label: 'Active'\n                },\n                {\n                    value: 'inactive',\n                    label: 'Inactive'\n                }\n            ]\n        },\n        {\n            key: 'parent',\n            label: 'Parent Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'root',\n                    label: 'Root Categories'\n                },\n                {\n                    value: 'sub',\n                    label: 'Sub Categories'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            fetchCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            filterAndSortCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], [\n        categories,\n        searchQuery,\n        currentFilters\n    ]);\n    const filterAndSortCategories = ()=>{\n        let filtered = [\n            ...categories\n        ];\n        // Apply search filter\n        if (searchQuery.trim()) {\n            const searchLower = searchQuery.toLowerCase();\n            filtered = filtered.filter((category)=>category.name.toLowerCase().includes(searchLower) || category.description && category.description.toLowerCase().includes(searchLower));\n        }\n        // Apply status filter\n        if (currentFilters.status) {\n            if (currentFilters.status === 'active') {\n                filtered = filtered.filter((category)=>category.isActive);\n            } else if (currentFilters.status === 'inactive') {\n                filtered = filtered.filter((category)=>!category.isActive);\n            }\n        }\n        // Apply parent filter\n        if (currentFilters.parent) {\n            if (currentFilters.parent === 'root') {\n                filtered = filtered.filter((category)=>!category.parentId);\n            } else if (currentFilters.parent === 'sub') {\n                filtered = filtered.filter((category)=>category.parentId);\n            }\n        }\n        setFilteredCategories(filtered);\n    };\n    const buildCategoryTree = (flatCategories)=>{\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // Transform and create map\n        flatCategories.forEach((cat)=>{\n            const category = {\n                id: String(cat.id),\n                name: cat.categname || cat.name,\n                description: cat.categdesc || cat.description,\n                parentId: cat.parentid ? String(cat.parentid) : undefined,\n                isActive: cat.isactive,\n                displayOrder: cat.displayorder || 0,\n                children: [],\n                _count: cat._count\n            };\n            categoryMap.set(category.id, category);\n        });\n        // Build tree structure\n        categoryMap.forEach((category)=>{\n            if (category.parentId && categoryMap.has(category.parentId)) {\n                categoryMap.get(category.parentId).children.push(category);\n            } else {\n                rootCategories.push(category);\n            }\n        });\n        // Sort by display order\n        const sortCategories = (cats)=>{\n            cats.sort((a, b)=>a.displayOrder - b.displayOrder);\n            cats.forEach((cat)=>{\n                if (cat.children) {\n                    sortCategories(cat.children);\n                }\n            });\n        };\n        sortCategories(rootCategories);\n        return rootCategories;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/categories?limit=100');\n            if (response.ok) {\n                const data = await response.json();\n                const categoriesData = data.data || data.categories || [];\n                setCategories(buildCategoryTree(categoriesData));\n            } else {\n                console.error('Failed to fetch categories:', response.status, response.statusText);\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (category)=>{\n        setEditingCategory(category);\n        setFormData({\n            name: category.name,\n            description: category.description || '',\n            parentId: category.parentId || '',\n            isActive: category.isActive,\n            displayOrder: category.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDelete = async (category)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchCategories();\n                if ((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id) {\n                    onCategorySelect(null);\n                }\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to delete category');\n            }\n        } catch (error) {\n            console.error('Error deleting category:', error);\n            alert('An error occurred while deleting the category');\n        }\n    };\n    const handleToggleActive = async (category)=>{\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    categname: category.name,\n                    categdesc: category.description,\n                    parentid: category.parentId ? Number(category.parentId) : 0,\n                    isactive: !category.isActive,\n                    displayorder: category.displayOrder\n                })\n            });\n            if (response.ok) {\n                fetchCategories();\n            }\n        } catch (error) {\n            console.error('Error toggling category status:', error);\n        }\n    };\n    const toggleExpanded = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const renderCategory = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _categories_find, _category_children;\n        const isExpanded = expandedCategories.has(category.id);\n        const hasChildren = category.children && category.children.length > 0;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-2 rounded-none cursor-pointer transition-colors border border-gray-200 \".concat(isSelected ? 'bg-blue-50 border-blue-300' : 'bg-white hover:bg-gray-50'),\n                    onClick: ()=>onCategorySelect(category),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-4\",\n                            style: {\n                                marginLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    toggleExpanded(category.id);\n                                },\n                                className: \"p-0.5 hover:bg-gray-200 rounded-none\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 19\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 19\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-full flex items-center justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas \".concat(getCategoryIcon(category), \" text-3xl\")\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-base truncate \".concat(isSelected ? 'text-gray-900' : 'text-gray-900'),\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, _this),\n                                category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm truncate mt-0.5 \".concat(isSelected ? 'text-gray-600' : 'text-gray-600'),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32 flex items-center\",\n                            children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600 truncate block\",\n                                children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400 italic\",\n                                children: \"Root\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 flex items-center\",\n                            children: category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 px-2 py-0.5 rounded-none text-sm font-medium\",\n                                children: category._count.services\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                children: category.isActive ? 'Active' : 'Inactive'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEdit(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Edit category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleToggleActive(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-blue-400 hover:bg-blue-500 focus:ring-blue-500' : 'bg-blue-300 hover:bg-blue-400 focus:ring-blue-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                    title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 23\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 23\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDelete(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Delete category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 19\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-6\",\n                    children: (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.map((child)=>renderCategory(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, _this);\n    };\n    const handleCreateClick = ()=>{\n        setIsFormOpen(true);\n        setEditingCategory(null);\n        setFormData({\n            name: '',\n            description: '',\n            parentId: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setCurrentFilters(newFilters);\n    };\n    const renderCategoryCard = function(category) {\n        let isLargeCard = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        var _category__count, _category__count1;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        const hasChildren = category.children && category.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group relative bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-300 overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md \".concat(isSelected ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300' : 'hover:bg-gray-50/50 hover:border-gray-300', \" \").concat(isLargeCard ? 'p-5' : 'p-4'),\n            onClick: ()=>onCategorySelect(category),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3 flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 p-2 bg-blue-50 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas \".concat(getCategoryIcon(category), \" text-2xl\")\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-lg text-gray-900 truncate mb-2\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, _this),\n                                        category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 line-clamp-2 leading-relaxed\",\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 rounded-full mr-1.5 \".concat(category.isActive ? 'bg-green-400' : 'bg-red-400')\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 11\n                                }, _this),\n                                category.isActive ? 'Active' : 'Inactive'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 9\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-100 my-1\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4 mb-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                    children: \"Parent\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-gray-600\",\n                                    children: category.parentId ? 'Sub-category' : 'Main Category'\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-1 py-0 bg-blue-100 text-blue-800 text-sm font-medium rounded-md\",\n                                        children: ((_category__count = category._count) === null || _category__count === void 0 ? void 0 : _category__count.services) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500 pt-1 border-t border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Children:\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gray-100 px-1 py-0 rounded\",\n                                    children: ((_category__count1 = category._count) === null || _category__count1 === void 0 ? void 0 : _category__count1.children) || 0\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Order:\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: category.displayOrder\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3 bottom-3 w-12 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out transform translate-x-full group-hover:translate-x-0 flex flex-col items-center justify-center space-y-4 z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleEdit(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110\",\n                            title: \"Edit Category\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleToggleActive(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 border rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110 \".concat(category.isActive ? 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600 text-white focus:ring-green-500' : 'bg-orange-500 hover:bg-orange-600 border-orange-400 hover:border-orange-500 text-white focus:ring-orange-500'),\n                            title: category.isActive ? 'Deactivate Category' : 'Activate Category',\n                            children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleDelete(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110\",\n                            title: \"Delete Category\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 670,\n                    columnNumber: 9\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 590,\n            columnNumber: 7\n        }, _this);\n    };\n    const getAllCategories = (cats)=>{\n        let all = [];\n        cats.forEach((cat)=>{\n            all.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                all = all.concat(getAllCategories(cat.children));\n            }\n        });\n        return all;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search categories by name or description...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"View:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode('list'),\n                                                className: \"px-3 py-2 rounded-md transition-colors flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                title: \"List view\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"List\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode('grid'),\n                                                className: \"px-3 py-2 rounded-md transition-colors flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                title: \"Grid view\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Grid\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCreateClick,\n                                className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Category\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 733,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 794,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading categories...\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 793,\n                    columnNumber: 11\n                }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No categories found\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: searchQuery || Object.keys(currentFilters).some((key)=>currentFilters[key]) ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first category.'\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCreateClick,\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Category\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 798,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: filteredCategories.map((category)=>renderCategory(category))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 817,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, false))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    ref: elementRef,\n                    initial: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    style: {\n                        position: 'fixed',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        zIndex: 9999,\n                        cursor: isDragging ? 'grabbing' : 'default',\n                        width: \"\".concat(size.width, \"px\"),\n                        maxHeight: \"\".concat(size.height, \"px\")\n                    },\n                    className: \"bg-white rounded-xl shadow-2xl border border-gray-200 p-0 overflow-hidden draggable-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800 px-4 py-3 flex items-center justify-between cursor-move select-none border-b border-gray-700\",\n                            onMouseDown: handleMouseDown,\n                            style: {\n                                cursor: 'move',\n                                userSelect: 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-medium text-sm\",\n                                    children: editingCategory ? 'Edit Category' : 'New Category'\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setIsFormOpen(false),\n                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 879,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 overflow-y-auto cursor-default form-content scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\",\n                            style: {\n                                maxHeight: \"\".concat(size.height - 80, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: async (e)=>{\n                                    e.preventDefault();\n                                    try {\n                                        const url = editingCategory ? \"/api/admin/categories/\".concat(editingCategory.id) : '/api/admin/categories';\n                                        const method = editingCategory ? 'PUT' : 'POST';\n                                        const response = await fetch(url, {\n                                            method,\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                categname: formData.name,\n                                                categdesc: formData.description,\n                                                parentid: formData.parentId ? Number(formData.parentId) : 0,\n                                                isactive: formData.isActive,\n                                                displayorder: formData.displayOrder\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            setIsFormOpen(false);\n                                            fetchCategories();\n                                        } else {\n                                            const errorData = await response.json();\n                                            alert(errorData.message || 'Failed to save category');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error saving category:', error);\n                                        alert('An error occurred while saving the category');\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                        children: \"Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        required: true,\n                                                        maxLength: 50,\n                                                        value: formData.name,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                name: e.target.value\n                                                            }),\n                                                        placeholder: \"Category name\",\n                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            formData.name.length,\n                                                            \"/50\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 935,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        placeholder: \"Brief description\",\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 952,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                        children: \"Parent Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.parentId,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                parentId: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"No parent (root category)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            categories.filter((cat)=>!editingCategory || cat.id !== editingCategory.id).map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: cat.id,\n                                                                    children: cat.name\n                                                                }, cat.id, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 966,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Display Order\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                min: \"0\",\n                                                                value: formData.displayOrder,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        displayOrder: Number(e.target.value)\n                                                                    }),\n                                                                className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.isActive,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                isActive: e.target.checked\n                                                                            }),\n                                                                        className: \"h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1006,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-xs text-gray-700\",\n                                                                        children: formData.isActive ? 'Active' : 'Inactive'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1012,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 987,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 pt-2 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1022,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 rounded-lg shadow-sm transition-all\",\n                                                children: editingCategory ? '💾 Update' : '✨ Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 899,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 right-0 w-8 h-8 cursor-se-resize bg-blue-500 hover:bg-blue-600 rounded-tl-lg transition-colors shadow-lg border-2 border-white\",\n                            onMouseDown: handleResizeMouseDown,\n                            style: {\n                                cursor: 'se-resize'\n                            },\n                            title: \"Drag to resize\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M22 22H20V20H22V22ZM22 18H20V16H22V18ZM18 22H16V20H18V22ZM18 18H16V16H18V18ZM14 22H12V20H14V22ZM22 14H20V12H22V14Z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 1047,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 1046,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 1040,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 858,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n        lineNumber: 731,\n        columnNumber: 5\n    }, this);\n}\n_s1(CategoryManagement, \"CMu7u2zDlZaIlTAaDV+gJ50a358=\", false, function() {\n    return [\n        useDraggableResizable\n    ];\n});\n_c = CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/category-management.tsx\n"));

/***/ })

});