"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/category-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/category-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryManagement: () => (/* binding */ CategoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ CategoryManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Function to get category-specific icons based on category name/type\nconst getCategoryIcon = (category)=>{\n    const name = category.name.toLowerCase();\n    // Web Development related\n    if (name.includes('web') || name.includes('website') || name.includes('frontend') || name.includes('backend')) {\n        return 'fa-globe text-blue-500';\n    }\n    // Mobile Development\n    if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) {\n        return 'fa-mobile-alt text-green-500';\n    }\n    // Design related\n    if (name.includes('design') || name.includes('ui') || name.includes('ux') || name.includes('graphic')) {\n        return 'fa-palette text-purple-500';\n    }\n    // E-commerce\n    if (name.includes('ecommerce') || name.includes('shop') || name.includes('store') || name.includes('commerce')) {\n        return 'fa-shopping-cart text-orange-500';\n    }\n    // Marketing\n    if (name.includes('marketing') || name.includes('seo') || name.includes('social') || name.includes('advertising')) {\n        return 'fa-bullhorn text-red-500';\n    }\n    // Consulting\n    if (name.includes('consulting') || name.includes('strategy') || name.includes('business')) {\n        return 'fa-handshake text-indigo-500';\n    }\n    // Support/Maintenance\n    if (name.includes('support') || name.includes('maintenance') || name.includes('hosting')) {\n        return 'fa-tools text-gray-500';\n    }\n    // Security\n    if (name.includes('security') || name.includes('ssl') || name.includes('backup')) {\n        return 'fa-shield-alt text-yellow-500';\n    }\n    // Analytics\n    if (name.includes('analytics') || name.includes('tracking') || name.includes('report')) {\n        return 'fa-chart-line text-teal-500';\n    }\n    // Default icons\n    if (category.parentId) {\n        return 'fa-tag text-orange-500' // Subcategory\n        ;\n    }\n    return 'fa-layer-group text-blue-500' // Parent category\n    ;\n};\n// Custom drag and resize hook\nconst useDraggableResizable = (isFormOpen)=>{\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [size, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 500,\n        height: 600\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [resizeStart, setResizeStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n    });\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Optimized mouse move handler with throttling\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseMove]\": (e)=>{\n            if (isDragging) {\n                const newX = e.clientX - dragStart.x;\n                const newY = e.clientY - dragStart.y;\n                // Constraints\n                const maxX = window.innerWidth - 200;\n                const maxY = window.innerHeight - 100;\n                const minX = -300;\n                const minY = -200;\n                const constrainedPosition = {\n                    x: Math.max(minX, Math.min(maxX, newX)),\n                    y: Math.max(minY, Math.min(maxY, newY))\n                };\n                setPosition(constrainedPosition);\n                // Direct DOM manipulation\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('left', \"calc(50% + \".concat(constrainedPosition.x, \"px)\"), 'important');\n                    elementRef.current.style.setProperty('top', \"calc(50% + \".concat(constrainedPosition.y, \"px)\"), 'important');\n                    elementRef.current.style.setProperty('transform', 'none', 'important');\n                }\n            } else if (isResizing) {\n                const deltaX = e.clientX - resizeStart.x;\n                const deltaY = e.clientY - resizeStart.y;\n                const newWidth = Math.max(400, Math.min(800, resizeStart.width + deltaX));\n                const newHeight = Math.max(300, Math.min(700, resizeStart.height + deltaY));\n                setSize({\n                    width: newWidth,\n                    height: newHeight\n                });\n                // Update modal size\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('width', \"\".concat(newWidth, \"px\"), 'important');\n                    elementRef.current.style.setProperty('max-height', \"\".concat(newHeight, \"px\"), 'important');\n                }\n            }\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseMove]\"], [\n        isDragging,\n        isResizing,\n        dragStart,\n        resizeStart\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            setIsResizing(false);\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseUp]\"], []);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragging(true);\n            setDragStart({\n                x: e.clientX - position.x,\n                y: e.clientY - position.y\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseDown]\"], [\n        position\n    ]);\n    const handleResizeMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleResizeMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsResizing(true);\n            setResizeStart({\n                width: size.width,\n                height: size.height,\n                x: e.clientX,\n                y: e.clientY\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleResizeMouseDown]\"], [\n        size\n    ]);\n    // Event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isDragging || isResizing) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n            }\n            return ({\n                \"useDraggableResizable.useEffect\": ()=>{\n                    document.removeEventListener('mousemove', handleMouseMove);\n                    document.removeEventListener('mouseup', handleMouseUp);\n                }\n            })[\"useDraggableResizable.useEffect\"];\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    // Reset when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isFormOpen) {\n                setPosition({\n                    x: 0,\n                    y: 0\n                });\n                setSize({\n                    width: 500,\n                    height: 600\n                });\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('left', '50%', 'important');\n                    elementRef.current.style.setProperty('top', '50%', 'important');\n                    elementRef.current.style.setProperty('transform', 'translate(-50%, -50%)', 'important');\n                    elementRef.current.style.setProperty('width', '500px', 'important');\n                    elementRef.current.style.setProperty('max-height', '600px', 'important');\n                }\n            }\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isFormOpen\n    ]);\n    return {\n        position,\n        size,\n        isDragging,\n        isResizing,\n        handleMouseDown,\n        handleResizeMouseDown,\n        elementRef\n    };\n};\n_s(useDraggableResizable, \"HKfFlj2o7uMYOp6GelXY4F24h+I=\");\nfunction CategoryManagement(param) {\n    let { selectedCategory, onCategorySelect } = param;\n    var _this = this;\n    _s1();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentFilters, setCurrentFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('comfortable');\n    const { position, size, isDragging, isResizing, handleMouseDown, handleResizeMouseDown, elementRef } = useDraggableResizable(isFormOpen);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        parentId: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    const filters = [\n        {\n            key: 'status',\n            label: 'Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Status'\n                },\n                {\n                    value: 'active',\n                    label: 'Active'\n                },\n                {\n                    value: 'inactive',\n                    label: 'Inactive'\n                }\n            ]\n        },\n        {\n            key: 'parent',\n            label: 'Parent Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'root',\n                    label: 'Root Categories'\n                },\n                {\n                    value: 'sub',\n                    label: 'Sub Categories'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            fetchCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            filterAndSortCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], [\n        categories,\n        searchQuery,\n        currentFilters\n    ]);\n    const filterAndSortCategories = ()=>{\n        let filtered = [\n            ...categories\n        ];\n        // Apply search filter\n        if (searchQuery.trim()) {\n            const searchLower = searchQuery.toLowerCase();\n            filtered = filtered.filter((category)=>category.name.toLowerCase().includes(searchLower) || category.description && category.description.toLowerCase().includes(searchLower));\n        }\n        // Apply status filter\n        if (currentFilters.status) {\n            if (currentFilters.status === 'active') {\n                filtered = filtered.filter((category)=>category.isActive);\n            } else if (currentFilters.status === 'inactive') {\n                filtered = filtered.filter((category)=>!category.isActive);\n            }\n        }\n        // Apply parent filter\n        if (currentFilters.parent) {\n            if (currentFilters.parent === 'root') {\n                filtered = filtered.filter((category)=>!category.parentId);\n            } else if (currentFilters.parent === 'sub') {\n                filtered = filtered.filter((category)=>category.parentId);\n            }\n        }\n        setFilteredCategories(filtered);\n    };\n    const buildCategoryTree = (flatCategories)=>{\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // Transform and create map\n        flatCategories.forEach((cat)=>{\n            const category = {\n                id: String(cat.id),\n                name: cat.categname || cat.name,\n                description: cat.categdesc || cat.description,\n                parentId: cat.parentid ? String(cat.parentid) : undefined,\n                isActive: cat.isactive,\n                displayOrder: cat.displayorder || 0,\n                children: [],\n                _count: cat._count\n            };\n            categoryMap.set(category.id, category);\n        });\n        // Build tree structure\n        categoryMap.forEach((category)=>{\n            if (category.parentId && categoryMap.has(category.parentId)) {\n                categoryMap.get(category.parentId).children.push(category);\n            } else {\n                rootCategories.push(category);\n            }\n        });\n        // Sort by display order\n        const sortCategories = (cats)=>{\n            cats.sort((a, b)=>a.displayOrder - b.displayOrder);\n            cats.forEach((cat)=>{\n                if (cat.children) {\n                    sortCategories(cat.children);\n                }\n            });\n        };\n        sortCategories(rootCategories);\n        return rootCategories;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/categories?limit=100');\n            if (response.ok) {\n                const data = await response.json();\n                const categoriesData = data.data || data.categories || [];\n                setCategories(buildCategoryTree(categoriesData));\n            } else {\n                console.error('Failed to fetch categories:', response.status, response.statusText);\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (category)=>{\n        setEditingCategory(category);\n        setFormData({\n            name: category.name,\n            description: category.description || '',\n            parentId: category.parentId || '',\n            isActive: category.isActive,\n            displayOrder: category.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDelete = async (category)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchCategories();\n                if ((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id) {\n                    onCategorySelect(null);\n                }\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to delete category');\n            }\n        } catch (error) {\n            console.error('Error deleting category:', error);\n            alert('An error occurred while deleting the category');\n        }\n    };\n    const handleToggleActive = async (category)=>{\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    categname: category.name,\n                    categdesc: category.description,\n                    parentid: category.parentId ? Number(category.parentId) : 0,\n                    isactive: !category.isActive,\n                    displayorder: category.displayOrder\n                })\n            });\n            if (response.ok) {\n                fetchCategories();\n            }\n        } catch (error) {\n            console.error('Error toggling category status:', error);\n        }\n    };\n    const toggleExpanded = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const renderCategory = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _categories_find, _category_children;\n        const isExpanded = expandedCategories.has(category.id);\n        const hasChildren = category.children && category.children.length > 0;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-2 rounded-none cursor-pointer transition-colors border border-gray-200 \".concat(isSelected ? 'bg-blue-50 border-blue-300' : 'bg-white hover:bg-gray-50'),\n                    onClick: ()=>onCategorySelect(category),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-4\",\n                            style: {\n                                marginLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    toggleExpanded(category.id);\n                                },\n                                className: \"p-0.5 hover:bg-gray-200 rounded-none\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 19\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 19\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-full flex items-center justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas \".concat(getCategoryIcon(category), \" text-3xl\")\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-base truncate \".concat(isSelected ? 'text-gray-900' : 'text-gray-900'),\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, _this),\n                                category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm truncate mt-0.5 \".concat(isSelected ? 'text-gray-600' : 'text-gray-600'),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32 flex items-center\",\n                            children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600 truncate block\",\n                                children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400 italic\",\n                                children: \"Root\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 flex items-center\",\n                            children: category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 px-2 py-0.5 rounded-none text-sm font-medium\",\n                                children: category._count.services\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                children: category.isActive ? 'Active' : 'Inactive'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEdit(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Edit category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleToggleActive(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-blue-400 hover:bg-blue-500 focus:ring-blue-500' : 'bg-blue-300 hover:bg-blue-400 focus:ring-blue-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                    title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 23\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 23\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDelete(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Delete category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 19\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-6\",\n                    children: (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.map((child)=>renderCategory(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, _this);\n    };\n    const handleCreateClick = ()=>{\n        setIsFormOpen(true);\n        setEditingCategory(null);\n        setFormData({\n            name: '',\n            description: '',\n            parentId: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setCurrentFilters(newFilters);\n    };\n    const renderCategoryCard = function(category) {\n        let isLargeCard = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        var _category__count, _category__count1;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        const hasChildren = category.children && category.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group relative bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-300 overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md \".concat(isSelected ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300' : 'hover:bg-gray-50/50 hover:border-gray-300', \" \").concat(isLargeCard ? 'p-5' : 'p-4'),\n            onClick: ()=>onCategorySelect(category),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3 flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 p-2 bg-blue-50 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas \".concat(getCategoryIcon(category), \" text-2xl\")\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-lg text-gray-900 truncate mb-2\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, _this),\n                                        category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 line-clamp-2 leading-relaxed\",\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 rounded-full mr-1.5 \".concat(category.isActive ? 'bg-green-400' : 'bg-red-400')\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 11\n                                }, _this),\n                                category.isActive ? 'Active' : 'Inactive'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 9\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-100 my-1\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4 mb-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                    children: \"Parent\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-gray-600\",\n                                    children: category.parentId ? 'Sub-category' : 'Main Category'\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-1 py-0 bg-blue-100 text-blue-800 text-sm font-medium rounded-md\",\n                                        children: ((_category__count = category._count) === null || _category__count === void 0 ? void 0 : _category__count.services) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500 pt-1 border-t border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Children:\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gray-100 px-1 py-0 rounded\",\n                                    children: ((_category__count1 = category._count) === null || _category__count1 === void 0 ? void 0 : _category__count1.children) || 0\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Order:\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: category.displayOrder\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3 bottom-3 w-12 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out transform translate-x-full group-hover:translate-x-0 flex flex-col items-center justify-center space-y-4 z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleEdit(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110\",\n                            title: \"Edit Category\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleToggleActive(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 border rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110 \".concat(category.isActive ? 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600 text-white focus:ring-green-500' : 'bg-orange-500 hover:bg-orange-600 border-orange-400 hover:border-orange-500 text-white focus:ring-orange-500'),\n                            title: category.isActive ? 'Deactivate Category' : 'Activate Category',\n                            children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleDelete(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110\",\n                            title: \"Delete Category\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 670,\n                    columnNumber: 9\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 590,\n            columnNumber: 7\n        }, _this);\n    };\n    const getAllCategories = (cats)=>{\n        let all = [];\n        cats.forEach((cat)=>{\n            all.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                all = all.concat(getAllCategories(cat.children));\n            }\n        });\n        return all;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search categories by name or description...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"View:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode('list'),\n                                                className: \"px-3 py-2 rounded-md transition-colors flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                title: \"List view\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"List\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode('grid'),\n                                                className: \"px-3 py-2 rounded-md transition-colors flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                title: \"Grid view\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Grid\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCreateClick,\n                                className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Category\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 733,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 794,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading categories...\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 793,\n                    columnNumber: 11\n                }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No categories found\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: searchQuery || Object.keys(currentFilters).some((key)=>currentFilters[key]) ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first category.'\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCreateClick,\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Category\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 798,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: filteredCategories.map((category)=>renderCategory(category))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 817,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, false))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    ref: elementRef,\n                    initial: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    style: {\n                        position: 'fixed',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        zIndex: 50,\n                        cursor: isDragging ? 'grabbing' : 'default',\n                        width: \"\".concat(size.width, \"px\"),\n                        maxHeight: \"\".concat(size.height, \"px\")\n                    },\n                    className: \"bg-white rounded-xl shadow-2xl border border-gray-200 p-0 overflow-hidden draggable-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-500 to-cyan-600 px-6 py-4 flex items-center justify-between cursor-move select-none\",\n                            onMouseDown: handleMouseDown,\n                            style: {\n                                cursor: 'move',\n                                userSelect: 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-folder text-white text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: editingCategory ? 'Edit Category' : 'New Category'\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-xs\",\n                                                    children: editingCategory ? 'Update category details' : 'Create a new service category'\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setIsFormOpen(false),\n                                    className: \"text-white/80 hover:text-white hover:bg-white/10 rounded-lg p-2 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 879,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 overflow-y-auto cursor-default form-content scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\",\n                            style: {\n                                maxHeight: \"\".concat(size.height - 120, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: async (e)=>{\n                                    e.preventDefault();\n                                    try {\n                                        const url = editingCategory ? \"/api/admin/categories/\".concat(editingCategory.id) : '/api/admin/categories';\n                                        const method = editingCategory ? 'PUT' : 'POST';\n                                        const response = await fetch(url, {\n                                            method,\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                categname: formData.name,\n                                                categdesc: formData.description,\n                                                parentid: formData.parentId ? Number(formData.parentId) : 0,\n                                                isactive: formData.isActive,\n                                                displayorder: formData.displayOrder\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            setIsFormOpen(false);\n                                            fetchCategories();\n                                        } else {\n                                            const errorData = await response.json();\n                                            alert(errorData.message || 'Failed to save category');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error saving category:', error);\n                                        alert('An error occurred while saving the category');\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-info-circle text-blue-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Basic Information\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Category Name *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 952,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        required: true,\n                                                                        maxLength: 50,\n                                                                        value: formData.name,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                name: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter category name (max 50 characters)\",\n                                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            formData.name.length,\n                                                                            \"/50 characters\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 964,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 968,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: formData.description,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                description: e.target.value\n                                                                            }),\n                                                                        rows: 3,\n                                                                        placeholder: \"Enter category description (optional)\",\n                                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 971,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-sitemap text-green-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Category Hierarchy\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Parent Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.parentId,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        parentId: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"\\uD83C\\uDFE0 No parent (root category)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    categories.filter((cat)=>!editingCategory || cat.id !== editingCategory.id).map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: cat.id,\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCC1 \",\n                                                                                cat.name\n                                                                            ]\n                                                                        }, cat.id, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: formData.parentId ? 'This will be a sub-category' : 'This will be a main category'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-cog text-purple-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1015,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Category Settings\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Display Order\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        min: \"0\",\n                                                                        value: formData.displayOrder,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                displayOrder: Number(e.target.value)\n                                                                            }),\n                                                                        placeholder: \"0\",\n                                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1023,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: \"Lower numbers appear first\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1031,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1019,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1035,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: formData.isActive,\n                                                                                        onChange: (e)=>setFormData({\n                                                                                                ...formData,\n                                                                                                isActive: e.target.checked\n                                                                                            }),\n                                                                                        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                                        lineNumber: 1040,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-2 text-sm text-gray-700\",\n                                                                                        children: \"Active\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                                        lineNumber: 1046,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                                lineNumber: 1039,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(formData.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                                children: formData.isActive ? '✓ Active' : '✗ Inactive'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                                lineNumber: 1048,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1038,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: formData.isActive ? 'Category will be visible to users' : 'Category will be hidden from users'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 pt-2 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 rounded-lg shadow-sm transition-all\",\n                                                children: editingCategory ? '💾 Update' : '✨ Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1073,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 907,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 right-0 w-8 h-8 cursor-se-resize bg-blue-500 hover:bg-blue-600 rounded-tl-lg transition-colors shadow-lg border-2 border-white\",\n                            onMouseDown: handleResizeMouseDown,\n                            style: {\n                                cursor: 'se-resize'\n                            },\n                            title: \"Drag to resize\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M22 22H20V20H22V22ZM22 18H20V16H22V18ZM18 22H16V20H18V22ZM18 18H16V16H18V18ZM14 22H12V20H14V22ZM22 14H20V12H22V14Z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 1084,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 858,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n        lineNumber: 731,\n        columnNumber: 5\n    }, this);\n}\n_s1(CategoryManagement, \"CMu7u2zDlZaIlTAaDV+gJ50a358=\", false, function() {\n    return [\n        useDraggableResizable\n    ];\n});\n_c = CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/category-management.tsx\n"));

/***/ })

});