"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/category-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/category-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryManagement: () => (/* binding */ CategoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ CategoryManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Function to get category-specific icons based on category name/type\nconst getCategoryIcon = (category)=>{\n    const name = category.name.toLowerCase();\n    // Web Development related\n    if (name.includes('web') || name.includes('website') || name.includes('frontend') || name.includes('backend')) {\n        return 'fa-globe text-blue-500';\n    }\n    // Mobile Development\n    if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) {\n        return 'fa-mobile-alt text-green-500';\n    }\n    // Design related\n    if (name.includes('design') || name.includes('ui') || name.includes('ux') || name.includes('graphic')) {\n        return 'fa-palette text-purple-500';\n    }\n    // E-commerce\n    if (name.includes('ecommerce') || name.includes('shop') || name.includes('store') || name.includes('commerce')) {\n        return 'fa-shopping-cart text-orange-500';\n    }\n    // Marketing\n    if (name.includes('marketing') || name.includes('seo') || name.includes('social') || name.includes('advertising')) {\n        return 'fa-bullhorn text-red-500';\n    }\n    // Consulting\n    if (name.includes('consulting') || name.includes('strategy') || name.includes('business')) {\n        return 'fa-handshake text-indigo-500';\n    }\n    // Support/Maintenance\n    if (name.includes('support') || name.includes('maintenance') || name.includes('hosting')) {\n        return 'fa-tools text-gray-500';\n    }\n    // Security\n    if (name.includes('security') || name.includes('ssl') || name.includes('backup')) {\n        return 'fa-shield-alt text-yellow-500';\n    }\n    // Analytics\n    if (name.includes('analytics') || name.includes('tracking') || name.includes('report')) {\n        return 'fa-chart-line text-teal-500';\n    }\n    // Default icons\n    if (category.parentId) {\n        return 'fa-tag text-orange-500' // Subcategory\n        ;\n    }\n    return 'fa-layer-group text-blue-500' // Parent category\n    ;\n};\n// Custom drag and resize hook\nconst useDraggableResizable = (isFormOpen)=>{\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [size, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 500,\n        height: 600\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [resizeStart, setResizeStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n    });\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Optimized mouse move handler with throttling\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseMove]\": (e)=>{\n            if (isDragging) {\n                const newX = e.clientX - dragStart.x;\n                const newY = e.clientY - dragStart.y;\n                // Constraints - Allow full screen dragging\n                const maxX = window.innerWidth - 100;\n                const maxY = window.innerHeight - 50;\n                const minX = -(window.innerWidth - 100);\n                const minY = -(window.innerHeight - 50);\n                const constrainedPosition = {\n                    x: Math.max(minX, Math.min(maxX, newX)),\n                    y: Math.max(minY, Math.min(maxY, newY))\n                };\n                setPosition(constrainedPosition);\n                // Direct DOM manipulation\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('left', \"calc(50% + \".concat(constrainedPosition.x, \"px)\"), 'important');\n                    elementRef.current.style.setProperty('top', \"calc(50% + \".concat(constrainedPosition.y, \"px)\"), 'important');\n                    elementRef.current.style.setProperty('transform', 'none', 'important');\n                }\n            } else if (isResizing) {\n                const deltaX = e.clientX - resizeStart.x;\n                const deltaY = e.clientY - resizeStart.y;\n                const newWidth = Math.max(400, Math.min(800, resizeStart.width + deltaX));\n                const newHeight = Math.max(300, Math.min(700, resizeStart.height + deltaY));\n                setSize({\n                    width: newWidth,\n                    height: newHeight\n                });\n                // Update modal size\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('width', \"\".concat(newWidth, \"px\"), 'important');\n                    elementRef.current.style.setProperty('max-height', \"\".concat(newHeight, \"px\"), 'important');\n                }\n            }\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseMove]\"], [\n        isDragging,\n        isResizing,\n        dragStart,\n        resizeStart\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n            setIsResizing(false);\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseUp]\"], []);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragging(true);\n            setDragStart({\n                x: e.clientX - position.x,\n                y: e.clientY - position.y\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleMouseDown]\"], [\n        position\n    ]);\n    const handleResizeMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useDraggableResizable.useCallback[handleResizeMouseDown]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setIsResizing(true);\n            setResizeStart({\n                width: size.width,\n                height: size.height,\n                x: e.clientX,\n                y: e.clientY\n            });\n        }\n    }[\"useDraggableResizable.useCallback[handleResizeMouseDown]\"], [\n        size\n    ]);\n    // Event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isDragging || isResizing) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n            }\n            return ({\n                \"useDraggableResizable.useEffect\": ()=>{\n                    document.removeEventListener('mousemove', handleMouseMove);\n                    document.removeEventListener('mouseup', handleMouseUp);\n                }\n            })[\"useDraggableResizable.useEffect\"];\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isDragging,\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    // Reset when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useDraggableResizable.useEffect\": ()=>{\n            if (isFormOpen) {\n                setPosition({\n                    x: 0,\n                    y: 0\n                });\n                setSize({\n                    width: 500,\n                    height: 600\n                });\n                if (elementRef.current) {\n                    elementRef.current.style.setProperty('left', '50%', 'important');\n                    elementRef.current.style.setProperty('top', '50%', 'important');\n                    elementRef.current.style.setProperty('transform', 'translate(-50%, -50%)', 'important');\n                    elementRef.current.style.setProperty('width', '500px', 'important');\n                    elementRef.current.style.setProperty('max-height', '600px', 'important');\n                }\n            }\n        }\n    }[\"useDraggableResizable.useEffect\"], [\n        isFormOpen\n    ]);\n    return {\n        position,\n        size,\n        isDragging,\n        isResizing,\n        handleMouseDown,\n        handleResizeMouseDown,\n        elementRef\n    };\n};\n_s(useDraggableResizable, \"HKfFlj2o7uMYOp6GelXY4F24h+I=\");\nfunction CategoryManagement(param) {\n    let { selectedCategory, onCategorySelect } = param;\n    var _this = this;\n    _s1();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentFilters, setCurrentFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('comfortable');\n    const { position, size, isDragging, isResizing, handleMouseDown, handleResizeMouseDown, elementRef } = useDraggableResizable(isFormOpen);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        parentId: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    const filters = [\n        {\n            key: 'status',\n            label: 'Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Status'\n                },\n                {\n                    value: 'active',\n                    label: 'Active'\n                },\n                {\n                    value: 'inactive',\n                    label: 'Inactive'\n                }\n            ]\n        },\n        {\n            key: 'parent',\n            label: 'Parent Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'root',\n                    label: 'Root Categories'\n                },\n                {\n                    value: 'sub',\n                    label: 'Sub Categories'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            fetchCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            filterAndSortCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], [\n        categories,\n        searchQuery,\n        currentFilters\n    ]);\n    const filterAndSortCategories = ()=>{\n        let filtered = [\n            ...categories\n        ];\n        // Apply search filter\n        if (searchQuery.trim()) {\n            const searchLower = searchQuery.toLowerCase();\n            filtered = filtered.filter((category)=>category.name.toLowerCase().includes(searchLower) || category.description && category.description.toLowerCase().includes(searchLower));\n        }\n        // Apply status filter\n        if (currentFilters.status) {\n            if (currentFilters.status === 'active') {\n                filtered = filtered.filter((category)=>category.isActive);\n            } else if (currentFilters.status === 'inactive') {\n                filtered = filtered.filter((category)=>!category.isActive);\n            }\n        }\n        // Apply parent filter\n        if (currentFilters.parent) {\n            if (currentFilters.parent === 'root') {\n                filtered = filtered.filter((category)=>!category.parentId);\n            } else if (currentFilters.parent === 'sub') {\n                filtered = filtered.filter((category)=>category.parentId);\n            }\n        }\n        setFilteredCategories(filtered);\n    };\n    const buildCategoryTree = (flatCategories)=>{\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // Transform and create map\n        flatCategories.forEach((cat)=>{\n            const category = {\n                id: String(cat.id),\n                name: cat.categname || cat.name,\n                description: cat.categdesc || cat.description,\n                parentId: cat.parentid ? String(cat.parentid) : undefined,\n                isActive: cat.isactive,\n                displayOrder: cat.displayorder || 0,\n                children: [],\n                _count: cat._count\n            };\n            categoryMap.set(category.id, category);\n        });\n        // Build tree structure\n        categoryMap.forEach((category)=>{\n            if (category.parentId && categoryMap.has(category.parentId)) {\n                categoryMap.get(category.parentId).children.push(category);\n            } else {\n                rootCategories.push(category);\n            }\n        });\n        // Sort by display order\n        const sortCategories = (cats)=>{\n            cats.sort((a, b)=>a.displayOrder - b.displayOrder);\n            cats.forEach((cat)=>{\n                if (cat.children) {\n                    sortCategories(cat.children);\n                }\n            });\n        };\n        sortCategories(rootCategories);\n        return rootCategories;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/categories?limit=100');\n            if (response.ok) {\n                const data = await response.json();\n                const categoriesData = data.data || data.categories || [];\n                setCategories(buildCategoryTree(categoriesData));\n            } else {\n                console.error('Failed to fetch categories:', response.status, response.statusText);\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (category)=>{\n        setEditingCategory(category);\n        setFormData({\n            name: category.name,\n            description: category.description || '',\n            parentId: category.parentId || '',\n            isActive: category.isActive,\n            displayOrder: category.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDelete = async (category)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchCategories();\n                if ((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id) {\n                    onCategorySelect(null);\n                }\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to delete category');\n            }\n        } catch (error) {\n            console.error('Error deleting category:', error);\n            alert('An error occurred while deleting the category');\n        }\n    };\n    const handleToggleActive = async (category)=>{\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    categname: category.name,\n                    categdesc: category.description,\n                    parentid: category.parentId ? Number(category.parentId) : 0,\n                    isactive: !category.isActive,\n                    displayorder: category.displayOrder\n                })\n            });\n            if (response.ok) {\n                fetchCategories();\n            }\n        } catch (error) {\n            console.error('Error toggling category status:', error);\n        }\n    };\n    const toggleExpanded = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const renderCategory = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _categories_find, _category_children;\n        const isExpanded = expandedCategories.has(category.id);\n        const hasChildren = category.children && category.children.length > 0;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-2 rounded-none cursor-pointer transition-colors border border-gray-200 \".concat(isSelected ? 'bg-blue-50 border-blue-300' : 'bg-white hover:bg-gray-50'),\n                    onClick: ()=>onCategorySelect(category),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-4\",\n                            style: {\n                                marginLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    toggleExpanded(category.id);\n                                },\n                                className: \"p-0.5 hover:bg-gray-200 rounded-none\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 19\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 19\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-full flex items-center justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas \".concat(getCategoryIcon(category), \" text-3xl\")\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0 flex flex-col justify-center ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-base truncate \".concat(isSelected ? 'text-gray-900' : 'text-gray-900'),\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, _this),\n                                category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm truncate mt-0.5 \".concat(isSelected ? 'text-gray-600' : 'text-gray-600'),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32 flex items-center\",\n                            children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600 truncate block\",\n                                children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400 italic\",\n                                children: \"Root\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 flex items-center\",\n                            children: category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 px-2 py-0.5 rounded-none text-sm font-medium\",\n                                children: category._count.services\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                children: category.isActive ? 'Active' : 'Inactive'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEdit(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Edit category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleToggleActive(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-blue-400 hover:bg-blue-500 focus:ring-blue-500' : 'bg-blue-300 hover:bg-blue-400 focus:ring-blue-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                    title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 23\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 23\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDelete(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-0.5 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Delete category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 19\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-6\",\n                    children: (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.map((child)=>renderCategory(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, _this);\n    };\n    const handleCreateClick = ()=>{\n        setIsFormOpen(true);\n        setEditingCategory(null);\n        setFormData({\n            name: '',\n            description: '',\n            parentId: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setCurrentFilters(newFilters);\n    };\n    const renderCategoryCard = function(category) {\n        let isLargeCard = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        var _category__count, _category__count1;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        const hasChildren = category.children && category.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group relative bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-300 overflow-hidden hover:cursor-pointer shadow-sm hover:shadow-md \".concat(isSelected ? 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/50 border-blue-300' : 'hover:bg-gray-50/50 hover:border-gray-300', \" \").concat(isLargeCard ? 'p-5' : 'p-4'),\n            onClick: ()=>onCategorySelect(category),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3 flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 p-2 bg-blue-50 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas \".concat(getCategoryIcon(category), \" text-2xl\")\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-lg text-gray-900 truncate mb-2\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, _this),\n                                        category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 line-clamp-2 leading-relaxed\",\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2.5 py-0 rounded-full text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 rounded-full mr-1.5 \".concat(category.isActive ? 'bg-green-400' : 'bg-red-400')\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 11\n                                }, _this),\n                                category.isActive ? 'Active' : 'Inactive'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 9\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-100 my-1\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4 mb-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                    children: \"Parent\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium text-gray-600\",\n                                    children: category.parentId ? 'Sub-category' : 'Main Category'\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-medium text-gray-500 uppercase tracking-wide\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-1 py-0 bg-blue-100 text-blue-800 text-sm font-medium rounded-md\",\n                                        children: ((_category__count = category._count) === null || _category__count === void 0 ? void 0 : _category__count.services) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 637,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500 pt-1 border-t border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Children:\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gray-100 px-1 py-0 rounded\",\n                                    children: ((_category__count1 = category._count) === null || _category__count1 === void 0 ? void 0 : _category__count1.children) || 0\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Order:\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: category.displayOrder\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3 bottom-3 w-12 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out transform translate-x-full group-hover:translate-x-0 flex flex-col items-center justify-center space-y-4 z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleEdit(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110\",\n                            title: \"Edit Category\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleToggleActive(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 border rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110 \".concat(category.isActive ? 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600 text-white focus:ring-green-500' : 'bg-orange-500 hover:bg-orange-600 border-orange-400 hover:border-orange-500 text-white focus:ring-orange-500'),\n                            title: category.isActive ? 'Deactivate Category' : 'Activate Category',\n                            children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                handleDelete(category);\n                            },\n                            className: \"group/btn relative inline-flex items-center justify-center w-8 h-8 bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer hover:scale-110\",\n                            title: \"Delete Category\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 transition-transform group-hover/btn:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 670,\n                    columnNumber: 9\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 590,\n            columnNumber: 7\n        }, _this);\n    };\n    const getAllCategories = (cats)=>{\n        let all = [];\n        cats.forEach((cat)=>{\n            all.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                all = all.concat(getAllCategories(cat.children));\n            }\n        });\n        return all;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-4 mb-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1 max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search categories by name or description...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"View:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode('list'),\n                                                className: \"px-3 py-2 rounded-md transition-colors flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                title: \"List view\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"List\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setViewMode('grid'),\n                                                className: \"px-3 py-2 rounded-md transition-colors flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                title: \"Grid view\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Grid\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCreateClick,\n                                className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Category\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 733,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 794,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading categories...\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 793,\n                    columnNumber: 11\n                }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No categories found\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: searchQuery || Object.keys(currentFilters).some((key)=>currentFilters[key]) ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first category.'\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCreateClick,\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Category\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 798,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: filteredCategories.map((category)=>renderCategory(category))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 817,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, false))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    ref: elementRef,\n                    initial: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0.95,\n                        opacity: 0\n                    },\n                    style: {\n                        position: 'fixed',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        zIndex: 9999,\n                        cursor: isDragging ? 'grabbing' : 'default',\n                        width: \"\".concat(size.width, \"px\"),\n                        maxHeight: \"\".concat(size.height, \"px\")\n                    },\n                    className: \"bg-white rounded-xl shadow-2xl border border-gray-200 p-0 overflow-hidden draggable-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-4 py-3 flex items-center justify-between cursor-move select-none border-b border-blue-800\",\n                            onMouseDown: handleMouseDown,\n                            style: {\n                                cursor: 'move',\n                                userSelect: 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-medium text-sm\",\n                                            children: editingCategory ? 'Edit Category' : 'New Category'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setIsFormOpen(false),\n                                    className: \"text-blue-200 hover:text-white transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 879,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 overflow-y-auto cursor-default form-content scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\",\n                            style: {\n                                maxHeight: \"\".concat(size.height - 80, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: async (e)=>{\n                                    e.preventDefault();\n                                    try {\n                                        const url = editingCategory ? \"/api/admin/categories/\".concat(editingCategory.id) : '/api/admin/categories';\n                                        const method = editingCategory ? 'PUT' : 'POST';\n                                        const response = await fetch(url, {\n                                            method,\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                categname: formData.name,\n                                                categdesc: formData.description,\n                                                parentid: formData.parentId ? Number(formData.parentId) : 0,\n                                                isactive: formData.isActive,\n                                                displayorder: formData.displayOrder\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            setIsFormOpen(false);\n                                            fetchCategories();\n                                        } else {\n                                            const errorData = await response.json();\n                                            alert(errorData.message || 'Failed to save category');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error saving category:', error);\n                                        alert('An error occurred while saving the category');\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                        children: \"Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        required: true,\n                                                        maxLength: 50,\n                                                        value: formData.name,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                name: e.target.value\n                                                            }),\n                                                        placeholder: \"Category name\",\n                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            formData.name.length,\n                                                            \"/50\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        placeholder: \"Brief description\",\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 955,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                        children: \"Parent Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.parentId,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                parentId: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"No parent (root category)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            categories.filter((cat)=>!editingCategory || cat.id !== editingCategory.id).map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: cat.id,\n                                                                    children: cat.name\n                                                                }, cat.id, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 982,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Display Order\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        min: \"0\",\n                                                                        value: formData.displayOrder,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                displayOrder: Number(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full px-3 py-2 pr-12 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-1 top-1/2 transform -translate-y-1/2 flex flex-col space-y-0.5\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setFormData({\n                                                                                        ...formData,\n                                                                                        displayOrder: formData.displayOrder + 1\n                                                                                    }),\n                                                                                className: \"w-5 h-2.5 flex items-center justify-center text-gray-400 hover:text-gray-600 text-xs leading-none\",\n                                                                                children: \"↑\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                                lineNumber: 1004,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setFormData({\n                                                                                        ...formData,\n                                                                                        displayOrder: Math.max(0, formData.displayOrder - 1)\n                                                                                    }),\n                                                                                className: \"w-5 h-2.5 flex items-center justify-center text-gray-400 hover:text-gray-600 text-xs leading-none\",\n                                                                                children: \"↓\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                                lineNumber: 1011,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 991,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1023,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center h-[36px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: formData.isActive,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                isActive: e.target.checked\n                                                                            }),\n                                                                        className: \"h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1027,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-xs text-gray-700\",\n                                                                        children: formData.isActive ? 'Active' : 'Inactive'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 1033,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 1026,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 990,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 936,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 pt-3 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-3 py-1.5 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors\",\n                                                children: editingCategory ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 1050,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 right-0 w-8 h-8 cursor-se-resize bg-blue-500 hover:bg-blue-600 rounded-tl-lg transition-colors shadow-lg border-2 border-white\",\n                            onMouseDown: handleResizeMouseDown,\n                            style: {\n                                cursor: 'se-resize'\n                            },\n                            title: \"Drag to resize\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M22 22H20V20H22V22ZM22 18H20V16H22V18ZM18 22H16V20H18V22ZM18 18H16V16H18V18ZM14 22H12V20H14V22ZM22 14H20V12H22V14Z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 1069,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 1067,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 858,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n        lineNumber: 731,\n        columnNumber: 5\n    }, this);\n}\n_s1(CategoryManagement, \"CMu7u2zDlZaIlTAaDV+gJ50a358=\", false, function() {\n    return [\n        useDraggableResizable\n    ];\n});\n_c = CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/category-management.tsx\n"));

/***/ })

});