/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/categories/[id]/route";
exports.ids = ["app/api/admin/categories/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_categories_id_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/categories/[id]/route.ts */ \"(rsc)/./src/app/api/admin/categories/[id]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/categories/[id]/route\",\n        pathname: \"/api/admin/categories/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/categories/[id]/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/categories/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_categories_id_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/categories/[id]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/categories/[id]/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/admin/categories/[id]/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mock-data */ \"(rsc)/./src/lib/mock-data.ts\");\n\n\nasync function GET(request, { params }) {\n    const { id: idParam } = await params;\n    const id = parseInt(idParam);\n    try {\n        // Temporarily disable authentication for testing\n        // const auth = await authenticateAdmin(request)\n        // if (!auth.isAdmin) {\n        //   return NextResponse.json(\n        //     { error: auth.error || 'Admin access required' },\n        //     { status: 401 }\n        //   )\n        // }\n        const category = _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories.find((c)=>c.id === id);\n        if (!category) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Category not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            data: category\n        });\n    } catch (error) {\n        console.error('Error fetching category:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch category'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    const { id: idParam } = await params;\n    const id = parseInt(idParam);\n    try {\n        // Temporarily disable authentication for testing\n        // const auth = await authenticateAdmin(request)\n        // if (!auth.isAdmin) {\n        //   return NextResponse.json(\n        //     { error: auth.error || 'Admin access required' },\n        //     { status: 401 }\n        //   )\n        // }\n        const body = await request.json();\n        const { categname, categdesc, parentid, isactive, displayorder } = body;\n        if (!categname) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Category name is required'\n            }, {\n                status: 400\n            });\n        }\n        const categoryIndex = _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories.findIndex((c)=>c.id === id);\n        if (categoryIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Category not found'\n            }, {\n                status: 404\n            });\n        }\n        // Update category\n        _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories[categoryIndex] = {\n            ..._lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories[categoryIndex],\n            categname,\n            categdesc: categdesc || '',\n            parentid: parentid || null,\n            isactive: isactive !== undefined ? isactive : _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories[categoryIndex].isactive,\n            displayorder: displayorder !== undefined ? displayorder : _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories[categoryIndex].displayorder\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            data: _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories[categoryIndex],\n            message: 'Category updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating category:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update category'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    const { id: idParam } = await params;\n    const id = parseInt(idParam);\n    try {\n        // Temporarily disable authentication for testing\n        // const auth = await authenticateAdmin(request)\n        // if (!auth.isAdmin) {\n        //   return NextResponse.json(\n        //     { error: auth.error || 'Admin access required' },\n        //     { status: 401 }\n        //   )\n        // }\n        const categoryIndex = _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories.findIndex((c)=>c.id === id);\n        if (categoryIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Category not found'\n            }, {\n                status: 404\n            });\n        }\n        // Check if category has children\n        const hasChildren = _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories.some((c)=>c.parentid === id);\n        if (hasChildren) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Cannot delete category with subcategories'\n            }, {\n                status: 400\n            });\n        }\n        // Check if category has services\n        if (_lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories[categoryIndex]._count.services > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Cannot delete category with services'\n            }, {\n                status: 400\n            });\n        }\n        // Delete category\n        const deletedCategory = _lib_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockCategories.splice(categoryIndex, 1)[0];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Category deleted successfully',\n            data: deletedCategory\n        });\n    } catch (error) {\n        console.error('Error deleting category:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete category'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/categories/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mock-data.ts":
/*!******************************!*\
  !*** ./src/lib/mock-data.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addBlogPost: () => (/* binding */ addBlogPost),\n/* harmony export */   deleteBlogPost: () => (/* binding */ deleteBlogPost),\n/* harmony export */   getBlogPost: () => (/* binding */ getBlogPost),\n/* harmony export */   mockBlogPosts: () => (/* binding */ mockBlogPosts),\n/* harmony export */   mockCategories: () => (/* binding */ mockCategories),\n/* harmony export */   updateBlogPost: () => (/* binding */ updateBlogPost)\n/* harmony export */ });\n// Mock data for categories - replace with database calls\nlet mockCategories = [\n    {\n        id: 1,\n        categname: 'Web Development',\n        categdesc: 'Custom web applications and websites',\n        parentid: null,\n        isactive: true,\n        displayorder: 1,\n        _count: {\n            services: 5,\n            children: 2\n        }\n    },\n    {\n        id: 2,\n        categname: 'Mobile Development',\n        categdesc: 'iOS and Android applications',\n        parentid: null,\n        isactive: true,\n        displayorder: 2,\n        _count: {\n            services: 3,\n            children: 1\n        }\n    },\n    {\n        id: 3,\n        categname: 'Cloud Services',\n        categdesc: 'Cloud infrastructure and deployment',\n        parentid: null,\n        isactive: true,\n        displayorder: 3,\n        _count: {\n            services: 4,\n            children: 0\n        }\n    },\n    {\n        id: 4,\n        categname: 'Frontend Development',\n        categdesc: 'React, Vue, Angular development',\n        parentid: 1,\n        isactive: true,\n        displayorder: 1,\n        _count: {\n            services: 2,\n            children: 0\n        }\n    },\n    {\n        id: 5,\n        categname: 'Backend Development',\n        categdesc: 'Node.js, Python, Java development',\n        parentid: 1,\n        isactive: true,\n        displayorder: 2,\n        _count: {\n            services: 3,\n            children: 0\n        }\n    }\n];\n// Shared mock data for blog posts\nlet mockBlogPosts = [\n    {\n        id: '1',\n        title: 'Getting Started with Next.js',\n        slug: 'getting-started-with-nextjs',\n        content: 'Next.js is a powerful React framework that makes building full-stack web applications simple and efficient...',\n        excerpt: 'Learn how to get started with Next.js and build your first application.',\n        featuredImageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',\n        authorId: 'admin-1',\n        isPublished: true,\n        publishedAt: '2024-01-15T10:00:00Z',\n        categories: 'Web Development, React',\n        tags: 'nextjs, react, javascript, tutorial',\n        createdAt: '2024-01-15T09:00:00Z',\n        updatedAt: '2024-01-15T10:00:00Z'\n    },\n    {\n        id: '2',\n        title: 'Building Modern UIs with Tailwind CSS',\n        slug: 'building-modern-uis-with-tailwind-css',\n        content: 'Tailwind CSS is a utility-first CSS framework that allows you to build custom designs without leaving your HTML...',\n        excerpt: 'Discover how to create beautiful, responsive user interfaces using Tailwind CSS.',\n        featuredImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',\n        authorId: 'admin-1',\n        isPublished: true,\n        publishedAt: '2024-01-14T14:30:00Z',\n        categories: 'CSS, Design',\n        tags: 'tailwind, css, design, ui',\n        createdAt: '2024-01-14T13:00:00Z',\n        updatedAt: '2024-01-14T14:30:00Z'\n    },\n    {\n        id: '3',\n        title: 'TypeScript Best Practices',\n        slug: 'typescript-best-practices',\n        content: 'TypeScript adds static typing to JavaScript, making your code more reliable and maintainable...',\n        excerpt: 'Explore the best practices for writing clean and maintainable TypeScript code.',\n        featuredImageUrl: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop',\n        authorId: 'admin-1',\n        isPublished: false,\n        publishedAt: null,\n        categories: 'Programming, TypeScript',\n        tags: 'typescript, javascript, programming, best-practices',\n        createdAt: '2024-01-13T16:00:00Z',\n        updatedAt: '2024-01-13T16:00:00Z'\n    },\n    {\n        id: '4',\n        title: 'Deploying Next.js Applications',\n        slug: 'deploying-nextjs-applications',\n        content: 'Learn how to deploy your Next.js applications to various platforms including Vercel, Netlify, and AWS...',\n        excerpt: 'A comprehensive guide to deploying Next.js applications to production.',\n        featuredImageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop',\n        authorId: 'admin-1',\n        isPublished: true,\n        publishedAt: '2024-01-12T11:00:00Z',\n        categories: 'Deployment, DevOps',\n        tags: 'nextjs, deployment, vercel, aws, devops',\n        createdAt: '2024-01-12T10:00:00Z',\n        updatedAt: '2024-01-12T11:00:00Z'\n    },\n    {\n        id: '5',\n        title: 'State Management in React',\n        slug: 'state-management-in-react',\n        content: 'Understanding state management is crucial for building complex React applications...',\n        excerpt: 'Explore different state management solutions for React applications.',\n        featuredImageUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',\n        authorId: 'admin-1',\n        isPublished: true,\n        publishedAt: '2024-01-11T15:00:00Z',\n        categories: 'React, State Management',\n        tags: 'react, state, redux, context, hooks',\n        createdAt: '2024-01-11T14:00:00Z',\n        updatedAt: '2024-01-11T15:00:00Z'\n    }\n];\n// Helper functions for blog post data manipulation\nconst addBlogPost = (post)=>{\n    mockBlogPosts.push(post);\n};\nconst updateBlogPost = (id, updates)=>{\n    const index = mockBlogPosts.findIndex((p)=>p.id === id);\n    if (index !== -1) {\n        mockBlogPosts[index] = {\n            ...mockBlogPosts[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n    }\n};\nconst deleteBlogPost = (id)=>{\n    const index = mockBlogPosts.findIndex((p)=>p.id === id);\n    if (index !== -1) {\n        mockBlogPosts.splice(index, 1);\n    }\n};\nconst getBlogPost = (id)=>{\n    return mockBlogPosts.find((p)=>p.id === id);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mock-data.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();